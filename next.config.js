/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable experimental features for better performance
  experimental: {
    // optimizePackageImports: ['@supabase/supabase-js'], // Disabled for webpack debugging
  },
  
  // Image optimization
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*.supabase.co',
        port: '',
        pathname: '/storage/v1/object/public/**',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'avatars.githubusercontent.com',
        port: '',
        pathname: '/**',
      },
    ],
    formats: ['image/webp', 'image/avif'],
  },
  
  // Security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=*, geolocation=()',
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=63072000; includeSubDomains; preload',
          },
          // Allow ngrok cross-origin requests
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization',
          },
        ],
      },
    ]
  },
  
  // Redirects for SEO
  async redirects() {
    return [
      // TEMPORARILY DISABLED HTTPS redirect to fix redirect loop
      // {
      //   source: '/:path*',
      //   has: [
      //     {
      //       type: 'header',
      //       key: 'x-forwarded-proto',
      //       value: 'http',
      //     },
      //   ],
      //   destination: 'https://:host/:path*',
      //   permanent: true,
      // },
      {
        source: '/home',
        destination: '/',
        permanent: true,
      },
      {
        source: '/signin',
        destination: '/login',
        permanent: true,
      },
      {
        source: '/signup',
        destination: '/register',
        permanent: true,
      },
    ]
  },
  
  // Compression
  compress: true,



  // Fix mobile touch events in production
  // swcMinify: false, // Re-enabled to fix webpack originalFactory error

  // ESLint configuration
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },

  // TypeScript configuration
  typescript: {
    // Warning: This allows production builds to successfully complete even if
    // your project has type errors.
    ignoreBuildErrors: true,
  },
  
  // PWA support (optional)
  // You can add PWA configuration here if needed
  
  // Bundle analyzer (for optimization)
  ...(process.env.ANALYZE === 'true' && {
    webpack: (config) => {
      config.plugins.push(
        new (require('@next/bundle-analyzer'))({
          enabled: true,
        })
      )
      return config
    },
  }),

  // Debug webpack module loading issues
  webpack: (config, { dev, isServer }) => {
    // Help resolve bad bare imports inside dependencies like epub2 (e.g., 'zipfile' -> './zipfile')
    config.resolve = config.resolve || {};
    config.resolve.preferRelative = true;
    if (!dev && !isServer) {
      // Add error handling for module factory issues
      config.optimization = config.optimization || {}
      config.optimization.moduleIds = 'deterministic'

      // Add debugging for failed module loads
      config.plugins.push({
        apply: (compiler) => {
          compiler.hooks.compilation.tap('DebugModuleFactory', (compilation) => {
            compilation.hooks.buildModule.tap('DebugModuleFactory', (module) => {
              if (module.request && module.request.includes('natural')) {
                console.log('Building module:', module.request)
              }
            })
          })
        }
      })
    }
    return config
  },
}

module.exports = nextConfig
