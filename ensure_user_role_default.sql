-- Check current default value for role column
SELECT column_name, column_default, data_type
FROM information_schema.columns
WHERE table_schema = 'public' 
AND table_name = 'users'
AND column_name = 'role';

-- FIX: Change default role from 'visitor' to 'user' for all new signups
ALTER TABLE users ALTER COLUMN role SET DEFAULT 'user';

-- Optional: Create a trigger to ensure no one gets 'visitor' role on insert
CREATE OR REPLACE FUNCTION ensure_user_role()
RETURNS TRIGGER AS $$
BEGIN
    -- Force all new users to have 'user' role, never 'visitor'
    IF NEW.role = 'visitor' OR NEW.role IS NULL THEN
        NEW.role := 'user';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS ensure_user_role_trigger ON users;
CREATE TRIGGER ensure_user_role_trigger
    BEFORE INSERT ON users
    FOR EACH ROW
    EXECUTE FUNCTION ensure_user_role();

-- Verify the changes
SELECT column_name, column_default, data_type
FROM information_schema.columns
WHERE table_schema = 'public' 
AND table_name = 'users'
AND column_name = 'role';