import { NextRequest, NextResponse } from 'next/server'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ groupId: string }> }
) {
  return NextResponse.json({ error: 'Group chat disabled' }, { status: 404 })
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ groupId: string }> }
) {
  return NextResponse.json({ error: 'Group chat disabled' }, { status: 404 })
}
