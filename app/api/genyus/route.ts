import { NextRequest } from "next/server";
import { z } from "zod";
import { createSupabaseServerClient } from "@/lib/supabase/client";
import { processGenyusRequest } from "@/lib/genyus/orchestrator";
import { checkRateLimit, recordRequest, createRateLimitError } from "@/lib/genyus/rate-limit";

export const runtime = "edge";

const bodySchema = z.object({
  question: z.string().min(1).max(8000),
  model: z.enum(['gpt5-nano', 'deepseek']).optional().default('gpt5-nano')
});

function resp(obj: any, status = 200) {
  return new Response(JSON.stringify(obj), {
    status,
    headers: { "Content-Type": "application/json" }
  });
}



export async function POST(req: NextRequest) {
  try {
    const json = await req.json();
    const { question, model } = bodySchema.parse(json);

    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return resp({ error: "Unauthorized" }, 401);
    }

    // Rate limiting
    if (!checkRateLimit(user.id)) {
      return createRateLimitError();
    }

    // Record this request
    recordRequest(user.id);

    // Check word balance
    const { data: bal, error: balError } = await supabase
      .rpc('ensure_user_has_words', { p_user_id: user.id })
      .single();
    const wordsRemaining = (bal as any)?.words_remaining;

    if (balError) {
      return resp({ error: "Failed to check word balance" }, 500);
    }

    // Check if user has enough words (most responses are 50+ words)
    const minimumWordsNeeded = 50;
    if (wordsRemaining == null || (wordsRemaining < minimumWordsNeeded && wordsRemaining !== -1)) {
      return resp({
        needsUpgrade: true,
        message: "Not enough words remaining. Upgrade to continue using Genyus.",
        wordsRemaining: wordsRemaining || 0
      }, 402);
    }

    // Process request through orchestrator
    const response = await processGenyusRequest({
      userId: user.id,
      question,
      model, // Pass user's model choice
    });
    const meta = await response.getMetadata().catch(() => ({ requestId: 'unknown' } as any));

    // Switch to Server-Sent Events to ensure immediate flush on mobile (Safari/WKWebView)
    // Transform the underlying string stream into SSE frames: "data: ...\n\n"
    const encoder = new TextEncoder();
    function toSSEFrame(chunk: string): Uint8Array {
      // Split by lines to be safe per SSE rules and prefix with "data: "
      const lines = chunk.split('\n');
      const framed = lines.map(line => `data: ${line}`).join('\n') + '\n\n';
      return encoder.encode(framed);
    }

    const transformedStream = new ReadableStream<Uint8Array>({
      async start(controller) {
        // Send an initial empty frame to force immediate flush on mobile browsers
        controller.enqueue(encoder.encode('data: \n\n'));

        const reader = response.stream.getReader();
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            if (value && value.length > 0) {
              controller.enqueue(toSSEFrame(value));
            }
          }
          // Send a final event to indicate end-of-stream
          controller.enqueue(encoder.encode('event: end\ndata: done\n\n'));
        } catch (error) {
          // Surface an SSE error event; consumers can decide how to handle
          controller.enqueue(encoder.encode(`event: error\ndata: ${String((error as Error)?.message || error)}\n\n`));
          controller.error(error);
        } finally {
          controller.close();
        }
      }
    });

    return new Response(transformedStream, {
      headers: {
        // SSE requires event-stream content type and keep-alive
        "Content-Type": "text/event-stream; charset=utf-8",
        "Cache-Control": "no-store, no-transform",
        "Connection": "keep-alive",
        // Helps disable proxy buffering in some environments
        "X-Accel-Buffering": "no",
        "X-Message-Id": meta.requestId
      }
    });

  } catch (error) {
    console.error('Genyus API error:', error);
    return resp({ error: "Internal server error" }, 500);
  }
}
