'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Day1Badge } from '@/components/Day1Badge'
import Image from 'next/image'
import { extractKeywords, getTopSimilarPosts } from '@/lib/contentAnalysis'
import { BlockButton } from '@/components/BlockButton'
import { PaywallContent } from '@/components/PaywallContent'
import { getSubscriptionStatus } from '@/lib/paywall'
import dynamic from 'next/dynamic'
const TimelineRealmFilters = dynamic(() => import('@/components/TimelineRealmFilters').then(m => m.TimelineRealmFilters), { ssr: false })

import { GlobalSearchBar } from '@/components/GlobalSearchBar'

import { SmartInviteModal } from '@/components/SmartInviteModal'
import { FirstPostModal } from '@/components/FirstPostModal'
import { UnifiedTimeline } from '@/components/UnifiedTimeline'
  const searchParams = typeof window !== 'undefined' ? new URLSearchParams(window.location.search) : null
  const experimentalStyle = searchParams?.get('style') === 'v2'


interface DiaryEntry {
  id: string
  title: string
  body_md: string
  created_at: string
  is_free: boolean
  bundle_count: number
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
  }
  loves_count: number
  comments_count: number
  has_access: boolean
  credits_required: number
  is_recent?: boolean
  days_old?: number
  photos?: Array<{ id: string; url: string; alt_text: string }>
  isRecommended?: boolean
  hasSubscription?: boolean
}

export default function TimelinePage() {
  console.log('TimelinePage: Component rendering start.')
  const [entries, setEntries] = useState<DiaryEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [readingEntryId, setReadingEntryId] = useState<string | null>(null)
  const [user, setUser] = useState<unknown>(null)
  const [hasError, setHasError] = useState(false) // New state for error
  const [showSignUpPrompt, setShowSignUpPrompt] = useState(false)
  const [viewedPosts, setViewedPosts] = useState(0)
  const router = useRouter()
  const supabase = createSupabaseClient()
  const [reloadKey, setReloadKey] = useState(0)
  const [filterEnabled, setFilterEnabled] = useState({ diary: true, audio: true, book: true, recipe: true, duo: true })


  const handleRealmChange = useCallback((_enabled: any) => {
    setReloadKey((k) => k + 1)
  }, [])


  useEffect(() => {
    console.log('Timeline: useEffect triggered, checking auth...')
    checkAuth()

    // Listen for auth state changes to refresh timeline
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      console.log('Timeline: Auth state changed:', event, session?.user?.id)
      if (event === 'SIGNED_IN' || event === 'SIGNED_OUT') {
        // Increment reload key to refresh timeline with new user context
        setReloadKey(k => k + 1)
        checkAuth()
      }
    })

    // Add a fallback to hide spinner after a delay, in case of unexpected hangs
    const timeoutId = setTimeout(() => {
      if (loading) { // Only set to false if still loading
        console.log('Timeline: Forcing loading to false after timeout.')
        setLoading(false)
      }
    }, 10000); // 10 seconds

    return () => {
      clearTimeout(timeoutId)
      subscription.unsubscribe()
    }
  }, []) // Remove checkAuth from dependencies to prevent loops

  const checkAuth = useCallback(async () => {
    try {
      console.log('Timeline: Attempting to get user session...')
      const { data: { user: authUser }, error: authUserError } = await supabase.auth.getUser()
      if (authUserError) {
        console.log('Timeline: Auth error, showing public timeline:', authUserError)
        // Show public timeline instead of redirecting
        setUser(null)
        loadPublicEntries()
        return
      }
      if (!authUser) {
        console.log('Timeline: No authenticated user found, showing public timeline.')
        setUser(null)
        loadPublicEntries()
        return
      }
      console.log('Timeline: User authenticated:', authUser.id)

      // Check if user exists in our users table (important for Google OAuth users)
      const { data: existingUser, error: userCheckError } = await supabase
        .from('users')
        .select('id')
        .eq('id', authUser.id)
        .single()

      if (userCheckError || !existingUser) {
        console.log('Timeline: Creating user profile for OAuth user')

        // Create user profile for OAuth users who don't have one yet
        const newUserData = {
          id: authUser.id,
          email: authUser.email!,
          name: authUser.user_metadata?.full_name || authUser.user_metadata?.name || null,
          avatar: authUser.user_metadata?.avatar_url || null,
          profile_picture_url: authUser.user_metadata?.avatar_url || null,
          role: 'user' as const
        }

        const { error: insertError } = await supabase
          .from('users')
          .insert(newUserData)

        if (insertError) {
          console.error('Timeline: Error creating user profile:', insertError)
          // Continue anyway - don't block the user
        } else {
          console.log('Timeline: User profile created successfully')
        }
      }

      setUser(authUser)
      loadEntries(authUser)
    } catch (error) {
      console.error('Timeline: Auth error in checkAuth:', error)
      // Don't redirect to login on auth errors - show public timeline instead
      setUser(null)
      loadPublicEntries()
      setLoading(false)
    }
  }, [supabase, router])

  const loadPublicEntries = async () => {
    try {
      console.log('Timeline: Loading public entries for anonymous user')

      // Get recent public diary entries from all users
      const { data, error: entriesError } = await supabase
        .from('diary_entries')
        .select(`
          id,
          title,
          body_md,
          created_at,
          is_free,
          love_count,
          user_id,
          keywords,
          user:users!user_id (
            id,
            name,
            avatar,
            profile_picture_url,
            has_day1_badge,
            signup_number
          )
        `)
        .eq('is_hidden', false)
        .eq('is_free', true) // Only show free content to anonymous users
        .order('created_at', { ascending: false })
        .limit(20)

      if (entriesError) {
        console.error('Timeline: Error fetching public entries:', entriesError)
        setHasError(true)
        setLoading(false)
        return
      }

      // Get photos for entries
      const entryIds = data?.map(entry => entry.id) || []
      const { data: photos } = await supabase
        .from('photos')
        .select('*')
        .in('diary_entry_id', entryIds)
        .eq('moderation_status', 'approved')
        .order('created_at', { ascending: true })

      // Group photos by entry ID
      const photosByEntry: { [key: string]: unknown[] } = {}
      photos?.forEach(photo => {
        if (!photosByEntry[photo.diary_entry_id]) {
          photosByEntry[photo.diary_entry_id] = []
        }
        photosByEntry[photo.diary_entry_id].push(photo)
      })

      // Add photos and access info to entries
      const entriesWithAccess = (data || []).map(entry => ({
        ...entry,
        photos: photosByEntry[entry.id] || [],
        isRecommended: false,
        hasSubscription: false,
        has_access: true // All public entries are accessible
      }))

      setEntries(entriesWithAccess as DiaryEntry[])
      console.log('Timeline: Public entries loaded successfully:', entriesWithAccess.length)
    } catch (error) {
      console.error('Timeline: Error loading public entries:', error)
      setHasError(true)
    } finally {
      setLoading(false)
    }
  }

  // Remove duplicate useEffect - already called above

  const loadEntries = useCallback(async (currentUser?: unknown) => {
    try {
      const userToUse = currentUser || user
      if (!userToUse) {
        console.log('Timeline: No user found for loading entries. Exiting loadEntries.')
        setLoading(false)
        return
      }

      console.log('Timeline: Starting to load entries for user:', (userToUse as any).id)

      // Get user's blocked users
      console.log('Timeline: Fetching blocked users...')
      const { data: blocks } = await supabase
        .from('blocks')
        .select('blocked_id')
        .eq('blocker_id', (userToUse as any).id)

      const blockedUserIds = blocks?.map(block => block.blocked_id) || []
      console.log('Timeline: Blocked users:', blockedUserIds)

      // Get user's follows (this is how users follow creators)
      console.log('Timeline: Fetching follows...')
      const { data: follows, error: followsError } = await supabase
        .from('follows')
        .select('*')
        .eq('follower_id', (userToUse as any).id)

      console.log('Timeline: Follows query result:', { follows, followsError })

      if (followsError) {
        console.error('Timeline: Error fetching follows:', {
          message: followsError.message,
          details: followsError.details,
          hint: followsError.hint,
          code: followsError.code
        })
        setEntries([])
        setHasError(true) // Set error state
        setLoading(false)
        return
      }

      if (!follows || follows.length === 0) {
        console.log('Timeline: No follows found for user. Setting empty entries.')
        // No follows = empty timeline
        setEntries([])
        setLoading(false)
        return
      }

      const creatorIds = follows
        .map(f => f.writer_id)
        .filter(id => !blockedUserIds.includes(id)) // Filter out blocked users
      console.log('Timeline: Looking for entries from creators:', creatorIds, '(filtered out', blockedUserIds.length, 'blocked users)')

      // Get entries from bookmarked creators, sorted by likes then date
      console.log('Timeline: Fetching diary entries...')
      const { data, error: entriesError } = await supabase
        .from('diary_entries')
        .select(`
          id,
          title,
          body_md,
          created_at,
          is_free,
          love_count,
          user_id,
          keywords,
          user:users!user_id (
            id,
            name,
            avatar,
            profile_picture_url,
            has_day1_badge,
            signup_number
          )
        `)
        .in('user_id', creatorIds)
        .eq('is_hidden', false)
        .order('created_at', { ascending: false })
        .order('love_count', { ascending: false })
        .limit(35) // Reduced to make room for recommendations

      console.log('Timeline: Entries query result:', { data, entriesError })

      if (entriesError) {
        console.error('Timeline: Error fetching entries:', entriesError)
        setEntries([])
        setHasError(true) // Set error state
        setLoading(false)
        return
      }

      // Get user's liked posts for recommendations
      console.log('Timeline: Fetching user likes for recommendations...')
      const { data: userLikes } = await supabase
        .from('loves')
        .select(`
          diary_entry_id,
          diary_entries!inner (
            id,
            title,
            body_md,
            keywords
          )
        `)
        .eq('user_id', (userToUse as any).id)
        .limit(20) // Recent likes for analysis

      // Get recommended posts from non-followed creators
      let recommendedEntries: any[] = []
      if (userLikes && userLikes.length > 0) {
        console.log('Timeline: Getting recommendations based on', userLikes.length, 'liked posts')

        // Extract keywords from liked posts
        const likedPostsWithKeywords = userLikes.map(like => {
          const entry = like.diary_entries as any
          return {
            id: entry.id,
            keywords: entry.keywords || extractKeywords(`${entry.title} ${entry.body_md}`)
          }
        })

        // Get Code Book user ID to exclude from recommendations
        const { data: codeBookUser } = await supabase
          .from('users')
          .select('id')
          .eq('name', 'OnlyDiary ChangeLog')
          .single()

        // Get potential posts from non-followed creators (excluding blocked users and Code Book)
        const excludedUserIds = [...creatorIds, ...blockedUserIds]
        if (codeBookUser?.id) {
          excludedUserIds.push(codeBookUser.id)
        }

        const { data: candidatePosts } = await supabase
          .from('diary_entries')
          .select(`
            id,
            title,
            body_md,
            created_at,
            is_free,
            love_count,
            user_id,
            keywords,
            user:users!user_id (
              id,
              name,
              avatar,
              profile_picture_url,
              has_day1_badge,
              signup_number
            )
          `)
          .not('user_id', 'in', `(${excludedUserIds.join(',')})`) // Exclude followed creators AND blocked users
          .eq('is_hidden', false)
          .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // Last 30 days
          .order('created_at', { ascending: false })
          .limit(100)

        if (candidatePosts && candidatePosts.length > 0) {
          // Prepare candidate posts with keywords
          const candidatesWithKeywords = candidatePosts.map(post => ({
            id: post.id,
            keywords: post.keywords || extractKeywords(`${post.title} ${post.body_md}`),
            post: post
          }))

          // Get similar post IDs
          const similarPostIds = getTopSimilarPosts(
            likedPostsWithKeywords,
            candidatesWithKeywords,
            15 // Get top 15 recommendations
          )

          // Get the actual recommended posts
          recommendedEntries = candidatePosts.filter(post =>
            similarPostIds.includes(post.id)
          ).slice(0, 15)

          console.log('Timeline: Found', recommendedEntries.length, 'recommended posts')
        }
      }

      // Get photos for all entries
      const entryIds = data?.map(entry => entry.id) || []
      console.log('Timeline: Fetching photos for entries:', entryIds)
      const { data: photos, error: photosError } = await supabase
        .from('photos')
        .select('*')
        .in('diary_entry_id', entryIds)
        .eq('moderation_status', 'approved')
        .order('created_at', { ascending: true })

      if (photosError) {
        console.error('Timeline: Error fetching photos:', photosError)
        // Continue without photos if there's an error, or handle as needed
      }

      // Group photos by entry ID
      const photosByEntry: { [key: string]: unknown[] } = {}
      photos?.forEach(photo => {
        if (!photosByEntry[photo.diary_entry_id]) {
          photosByEntry[photo.diary_entry_id] = []
        }
        photosByEntry[photo.diary_entry_id].push(photo)
      })

      // Combine followed and recommended entries
      const allEntries = [...(data || []), ...recommendedEntries]

      // Batch check subscription status for all entries (much more efficient)
      console.log('Timeline: Checking subscription status for entries...')
      const uniqueWriterIds = [...new Set(allEntries.map(entry => entry.user_id))]

      // Single query to get all subscriptions at once
      const { data: subscriptions } = await supabase
        .from('subscriptions')
        .select('writer_id, status')
        .eq('reader_id', (userToUse as any).id)
        .in('writer_id', uniqueWriterIds)
        .eq('status', 'active')
        .gte('current_period_end', new Date().toISOString())

      // Create a lookup map for faster access
      const subscriptionMap = new Set(subscriptions?.map(sub => sub.writer_id) || [])

      const entriesWithAccess = allEntries.map((entry) => {
        const hasSubscription = subscriptionMap.has(entry.user_id)

        return {
          ...entry,
          photos: photosByEntry[entry.id] || [],
          isRecommended: recommendedEntries.some(rec => rec.id === entry.id),
          hasSubscription,
          has_access: entry.is_free || hasSubscription || entry.user_id === (userToUse as any).id
        }
      })

      // Mix entries by creator and type (followed vs recommended)
      const followedEntries = entriesWithAccess.filter((entry: any) => !entry.isRecommended)
      const recEntries = entriesWithAccess.filter((entry: any) => entry.isRecommended)

      // Interleave: 2 followed posts, then 1 recommended post
      const mixedEntries: any[] = []
      let followedIndex = 0
      let recIndex = 0

      while (followedIndex < followedEntries.length || recIndex < recEntries.length) {
        // Add 2 followed posts
        for (let i = 0; i < 2 && followedIndex < followedEntries.length; i++) {
          mixedEntries.push(followedEntries[followedIndex++])
        }

        // Add 1 recommended post
        if (recIndex < recEntries.length) {
          mixedEntries.push(recEntries[recIndex++])
        }
      }

      console.log('Timeline: Final mixed entries count:', mixedEntries.length,
                  '(', followedEntries.length, 'followed +', recEntries.length, 'recommended )')
      setEntries(mixedEntries.slice(0, 50) as DiaryEntry[])
      console.log('Timeline: Entries loaded successfully.')
    } catch (error) {
      console.error('Timeline: Unexpected error in loadEntries:', error)
      setHasError(true) // Set error state
    } finally {
      console.log('Timeline: Setting loading to false.')
      setLoading(false)
    }
  }, [supabase]) // Add dependencies for useCallback

  const handleReadEntry = (entryId: string) => {
    setReadingEntryId(entryId)

    // Track post views for anonymous users
    if (!user) {
      const newViewCount = viewedPosts + 1
      setViewedPosts(newViewCount)

      // Show sign-up prompt after viewing 3 posts
      if (newViewCount >= 3) {
        setShowSignUpPrompt(true)
      }
    }

    router.push(`/d/${entryId}`)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (hasError) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
        <div className="bg-white rounded-2xl p-12 text-center shadow-sm max-w-md w-full">
          <h3 className="text-xl font-serif text-red-600 mb-2">Error Loading Timeline</h3>
          <p className="text-gray-600 mb-6">
            There was a problem loading your timeline. Please try again later or contact support.
          </p>
          <button
            onClick={() => {
              setHasError(false);
              setLoading(true);
              checkAuth(); // Re-attempt loading
            }}
            className="inline-block bg-blue-600 text-white px-6 py-3 rounded-xl font-medium hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 overflow-x-hidden">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 py-6 sm:py-8 w-full">
        <div className="mb-6 sm:mb-8" data-tutorial="timeline-header">
        <div className="mb-6">
          {/* Global Search */}
          <GlobalSearchBar />
        </div>

          <h1 className="text-2xl sm:text-3xl font-serif bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">Timeline</h1>
          <p className="text-gray-600 font-serif text-sm sm:text-base">Latest stories from creators</p>

          {/* Realm Filters */}
          <div className="mt-4">

            {/* Wire filters to reload timeline and apply client filter */}
            <TimelineRealmFilters onChange={(enabled) => {
              setFilterEnabled(enabled)
              setReloadKey((k) => k + 1)
            }} />

          </div>
        </div>

        {/* Show sign-up prompt for anonymous users */}
        {!user && (
          <div className="mb-8 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-100">
            <div className="text-center">
              <h3 className="text-xl font-serif text-gray-800 mb-2">
                Enjoying the stories?
              </h3>
              <p className="text-gray-600 mb-4">
                Join OnlyDiary to follow your favorite creators, get personalized recommendations, and never miss a story.
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Link
                  href="/register"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-300"
                >
                  Join OnlyDiary
                </Link>
                <Link
                  href="/login"
                  className="text-gray-600 hover:text-gray-800 px-6 py-3 rounded-xl font-medium border border-gray-300 hover:border-gray-400 transition-colors"
                >
                  Sign In
                </Link>
              </div>
            </div>
          </div>
        )}

        {/* Use UnifiedTimeline to show diary entries, audio posts, and books */}
        <UnifiedTimeline
          currentUserId={user?.id}
          onUserClick={(userId) => router.push(`/u/${userId}`)}
          reloadKey={reloadKey}
          filterEnabled={filterEnabled}
        />
      </div>

      {/* Smart Invite Modal - First Login */}
      {user && (
        <SmartInviteModal
          userId={user.id}
          userName={user.user_metadata?.name || user.email}
          trigger="first_login"
        />
      )}

      {/* First Post Modal - Universal */}
      {user && (
        <FirstPostModal
          userId={user.id}
          userName={user.user_metadata?.name || user.email}
        />
      )}

      {/* Sign-up prompt modal for anonymous users */}
      {showSignUpPrompt && !user && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl p-8 max-w-md w-full">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-2xl font-serif text-gray-800 mb-2">
                Love what you're reading?
              </h3>
              <p className="text-gray-600 mb-6">
                Join OnlyDiary to follow creators, get personalized recommendations, and support the writers you love.
              </p>
              <div className="flex flex-col gap-3">
                <Link
                  href="/register"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-300"
                >
                  Join OnlyDiary - It's Free
                </Link>
                <div className="flex gap-3">
                  <Link
                    href="/login"
                    className="flex-1 text-gray-600 hover:text-gray-800 px-4 py-2 rounded-xl font-medium border border-gray-300 hover:border-gray-400 transition-colors text-center"
                  >
                    Sign In
                  </Link>
                  <button
                    onClick={() => setShowSignUpPrompt(false)}
                    className="flex-1 text-gray-500 hover:text-gray-700 px-4 py-2 rounded-xl font-medium transition-colors"
                  >
                    Continue Browsing
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
