-- Check all users and their roles
SELECT 
    au.id,
    au.email,
    au.created_at,
    au.last_sign_in_at,
    au.raw_app_meta_data->>'provider' as auth_provider,
    au.raw_user_meta_data->>'full_name' as full_name,
    au.role as auth_role,
    CASE 
        WHEN au.raw_app_meta_data->>'provider' = 'google' THEN 'Google OAuth'
        WHEN au.raw_app_meta_data->>'provider' = 'email' THEN 'Email/Password'
        ELSE COALESCE(au.raw_app_meta_data->>'provider', 'Unknown')
    END as login_method
FROM auth.users au
ORDER BY au.created_at DESC;

-- Count users by role
SELECT 
    role as auth_role,
    COUNT(*) as user_count
FROM auth.users
GROUP BY role
ORDER BY user_count DESC;

-- Find any "visitor" role users who have logged in
SELECT 
    id,
    email,
    role,
    raw_app_meta_data->>'provider' as auth_provider,
    created_at,
    last_sign_in_at,
    CASE 
        WHEN last_sign_in_at IS NOT NULL THEN 'Has logged in'
        ELSE 'Never logged in'
    END as login_status
FROM auth.users
WHERE role = 'visitor' OR role IS NULL
ORDER BY last_sign_in_at DESC;

-- Check for any custom user_roles or profiles table
-- (Adjust table name if different in your schema)
SELECT 
    table_schema,
    table_name
FROM information_schema.tables
WHERE table_name LIKE '%role%' 
   OR table_name LIKE '%profile%'
   OR table_name LIKE '%user%'
AND table_schema NOT IN ('pg_catalog', 'information_schema', 'auth');

-- If you have a profiles table, check it
-- Uncomment and adjust if you have a profiles table
/*
SELECT 
    p.*,
    au.email,
    au.role as auth_role,
    au.raw_app_meta_data->>'provider' as auth_provider
FROM public.profiles p
JOIN auth.users au ON p.id = au.id
ORDER BY au.created_at DESC;
*/

-- Check RLS policies that might be affected by roles
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies
WHERE schemaname = 'public'
AND (qual LIKE '%role%' OR with_check LIKE '%role%' OR qual LIKE '%visitor%');