import { createClient } from "@supabase/supabase-js";
import { countChargeableWords } from "./word-count";
import { getCachedResponse, setCachedResponse } from "./cache";
import { moderateContent, shouldBlockContent, generateRefusalMessage } from "./moderation";
import { getConversationHistory, buildContextualPrompt, shouldUseContext } from "./memory";
import {
  getUserMemoryProfile,
  initializeUserMemory,
  updateUserMemoryProfile,
  getEnhancedConversationHistory,
  analyzeAndStoreConversationContext
} from "./enhanced-memory";
import { analyzeCommunicationStyle } from "./communication-analysis";
import { contextualRetriever } from "./contextual-retrieval";
import { smartOnboarding } from "./smart-onboarding";
import {
  callDeepSeekDirect,
  type ProviderResponse
} from "./providers";

export interface GenyusRequest {
  userId: string;
  question: string;
  requestId?: string;
  model?: 'haiku3' | 'deepseek';
}

export interface GenyusResponse {
  stream: ReadableStream<string>;
  getFinalText: () => Promise<string>;
  getWordCount: () => Promise<number>;
  getMetadata: () => Promise<{
    requestId: string;
    cached: boolean;
    latency: number;
    providerResponses: ProviderResponse[];
    moderatorLatency: number;
    wordCount: number;
    safetyFlags?: any;
  }>;
}

function createSupabaseServiceClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false
      }
    }
  );
}

export async function processGenyusRequest(request: GenyusRequest): Promise<GenyusResponse> {
  const { userId, question, model = 'haiku3' } = request;
  const supabase = createSupabaseServiceClient();
  
  // Check cache first
  const cachedResponse = await getCachedResponse(userId, question);
  if (cachedResponse) {
    return createCachedResponse(cachedResponse);
  }

  // Save request to database
  const { data: reqRow, error: reqErr } = await supabase
    .from("genyus_requests")
    .insert({ user_id: userId, question })
    .select()
    .single();

  if (reqErr) {
    throw new Error(`Failed to log request: ${reqErr.message}`);
  }

  const requestId = reqRow.id;
  let finalText = '';
  let wordCount = 0;
  let latency = 0;
  let safetyFlags: any = null;

  // 🎯 INVISIBLE INTELLIGENCE: Session-based chat with background growth analysis
  console.log(`🔮 Invisible Intelligence processing for user: ${userId}`);

  // Import invisible intelligence functions
  const { getSessionContext, getInvisibleIntelligence } = await import('./invisible-intelligence');

  // Load session context and intelligence in parallel for speed
  const [sessionContext, invisibleIntelligence] = await Promise.all([
    getSessionContext(userId).catch(error => {
      console.error('❌ Session context error:', error);
      return []; // Return empty context on error
    }),
    getInvisibleIntelligence(userId).catch(error => {
      console.error('❌ Invisible intelligence error:', error);
      return { currentPersonality: 'friendly_assistant', insights: [] }; // Return default on error
    })
  ]);

  console.log(`⚡ Session context ready: ${sessionContext.length} messages, personality: ${invisibleIntelligence.currentPersonality}`);

  if (sessionContext.length > 0) {
    console.log(`📝 Session context preview:`, sessionContext.slice(-2).map(m => `${m.role}: ${m.content.substring(0, 50)}...`));
  }

  // Build simple contextual messages (session only)
  const contextualMessages = sessionContext;

  // Enhanced user memory with invisible intelligence
  const enhancedUserMemory = {
    invisibleIntelligence,
    sessionLength: sessionContext.length,
    processingApproach: 'invisible_intelligence'
  };

  // 🎯 USER CHOICE: Use the model selected by the user
  const userModel = model; // User's choice from UI toggle

  let stream, getFinalText, getMetadata;

  if (userModel === 'deepseek') {
    console.log('🧠 User chose DeepSeek for deep thinking');
    const deepSeekResponse = await callDeepSeekDirect(question, contextualMessages, enhancedUserMemory);
    stream = deepSeekResponse.stream;
    getFinalText = deepSeekResponse.getFinalText;
    getMetadata = deepSeekResponse.getMetadata;
  } else {
    console.log('⚡ User chose Claude Haiku 3 for lightning speed');
    try {
      const { callClaudeHaiku3 } = await import('./providers');
      const haikuResponse = await callClaudeHaiku3(question, contextualMessages, enhancedUserMemory);
      stream = haikuResponse.stream;
      getFinalText = haikuResponse.getFinalText;
      getMetadata = haikuResponse.getMetadata;
    } catch (error) {
      // Check if it's a quota error and fallback to DeepSeek
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('insufficient_quota') || errorMessage.includes('quota') || errorMessage.includes('429')) {
        console.log('⚠️ Claude Haiku 3 quota exceeded, falling back to DeepSeek');
        const deepSeekResponse = await callDeepSeekDirect(question, contextualMessages, enhancedUserMemory);
        stream = deepSeekResponse.stream;
        getFinalText = deepSeekResponse.getFinalText;
        getMetadata = deepSeekResponse.getMetadata;
      } else {
        // Re-throw non-quota errors
        throw error;
      }
    }
  }
  
  // Create response stream that handles post-processing
  const processedStream = new ReadableStream<string>({
    async start(controller) {
      const reader = stream.getReader();
      
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          finalText += value;
          controller.enqueue(value);
        }
      } catch (error) {
        controller.error(error);
      } finally {
        controller.close();
        
        // Post-processing after stream completes
        (async () => {
          try {
            const deepseekMeta = await getMetadata();
            latency = deepseekMeta.latency;
            wordCount = countChargeableWords(finalText);

            // Safety moderation
            const moderation = await moderateContent(finalText);
            if (shouldBlockContent(moderation)) {
              safetyFlags = { flagged: true, categories: moderation.categories };
              // Don't deduct words for blocked content
              return;
            }

            // Deduct words atomically
            await deductWords(supabase, userId, wordCount);

            // Save final answer
            await supabase.from("genyus_answers").insert({
              request_id: requestId,
              moderator_model: "deepseek-chat",
              final_text: finalText,
              word_count: wordCount,
              latency_ms: latency,
              tokens_in: deepseekMeta.tokensIn,
              tokens_out: deepseekMeta.tokensOut,
              safety_flags: safetyFlags,
            });

            // Cache the response
            await setCachedResponse(userId, question, finalText);

            // Update user memory profile
            if (enhancedUserMemory?.adaptiveContext) {
              const updatedMemory = {
                ...enhancedUserMemory.adaptiveContext,
                interaction_count: (enhancedUserMemory.adaptiveContext.interaction_count || 0) + 1,
                last_interaction: new Date(),
                // Add question topic to recent topics
                recent_topics: [
                  ...((enhancedUserMemory.adaptiveContext.recent_topics || []).slice(-9)), // Keep last 9
                  question.substring(0, 100) // Add current (truncated)
                ]
              };

              // Update relationship stage based on interaction count
              if (updatedMemory.interaction_count >= 20 && updatedMemory.relationship_stage === 'developing') {
                updatedMemory.relationship_stage = 'established';
                updatedMemory.trust_level = Math.min(10, (updatedMemory.trust_level || 1) + 1);
              } else if (updatedMemory.interaction_count >= 5 && updatedMemory.relationship_stage === 'new') {
                updatedMemory.relationship_stage = 'developing';
                updatedMemory.trust_level = Math.min(10, (updatedMemory.trust_level || 1) + 1);
              }

              await updateUserMemoryProfile(userId, updatedMemory);
            }

            // Extract onboarding insights during learning phase (simplified for invisible intelligence)
            // Note: Onboarding insights now handled by invisible intelligence background analysis
            console.log('📚 Onboarding insights handled by invisible intelligence');

            // 🔮 INVISIBLE INTELLIGENCE: Background growth analysis (non-blocking)
            const { analyzeGrowthPatterns } = await import('./invisible-intelligence');
            analyzeGrowthPatterns(userId, question, finalText).catch(error => {
              console.error('Background growth analysis error:', error);
            });

            console.log('✨ Invisible Intelligence: Background analysis initiated');

            // 💾 AUTO-SAVE CONVERSATION SESSION (non-blocking)
            const { autoSaveConversationIfNeeded } = await import('./conversation-sessions');
            const currentMessages = [
              ...sessionContext,
              { role: 'user', content: question },
              { role: 'assistant', content: finalText }
            ];
            autoSaveConversationIfNeeded(userId, currentMessages, [requestId]).catch(error => {
              console.error('Auto-save conversation error:', error);
            });

          } catch (error) {
            console.error('Post-processing error:', error);
          }
        })();
      }
    }
  });

  return {
    stream: processedStream,
    getFinalText: async () => finalText,
    getWordCount: async () => wordCount,
    getMetadata: async () => ({
      requestId,
      cached: false,
      latency,
      wordCount,
      safetyFlags,
    }),
  };
}

// Helper functions
async function deductWords(supabase: any, userId: string, wordCount: number): Promise<void> {
  // Check if user has unlimited plan first
  const { data: userWords } = await supabase
    .from('genyus_user_words')
    .select('words_remaining, tier')
    .eq('user_id', userId)
    .single();

  // If unlimited plan (words_remaining = -1), don't deduct
  if (userWords?.words_remaining === -1 || userWords?.tier === 'unlimited') {
    return;
  }

  // Otherwise, deduct words normally
  const { error } = await supabase.rpc('genyus_decrement_words', {
    p_user_id: userId,
    p_words: wordCount
  });

  if (error) {
    console.error('Word deduction error:', error);
    throw new Error('Failed to deduct words');
  }
}

async function storeProviderResponses(
  supabase: any, 
  requestId: string, 
  responses: ProviderResponse[]
): Promise<void> {
  try {
    const records = responses.map(response => ({
      request_id: requestId,
      provider: response.provider,
      model: response.model,
      latency_ms: response.latency,
      tokens_in: response.tokensIn,
      tokens_out: response.tokensOut,
      raw_text: response.text,
      normalized_text: response.text, // Could add normalization logic here
      error: response.error,
    }));

    await supabase.from("genyus_provider_responses").insert(records);
  } catch (error) {
    console.error('Failed to store provider responses:', error);
    // Don't throw - this is not critical
  }
}

function createCachedResponse(cachedText: string): GenyusResponse {
  const stream = new ReadableStream<string>({
    start(controller) {
      // Stream cached response word by word for consistent UX
      const words = cachedText.split(' ');
      let index = 0;
      
      const streamWord = () => {
        if (index < words.length) {
          controller.enqueue(words[index] + ' ');
          index++;
          setTimeout(streamWord, 30); // Fast streaming for cached content
        } else {
          controller.close();
        }
      };
      
      streamWord();
    }
  });

  return {
    stream,
    getFinalText: async () => cachedText,
    getWordCount: async () => countChargeableWords(cachedText),
    getMetadata: async () => ({
      requestId: 'cached',
      cached: true,
      latency: 0,
      wordCount: countChargeableWords(cachedText),
    }),
  };
}

function createErrorResponse(
  errorText: string, 
  requestId: string, 
  providerResponses: ProviderResponse[]
): GenyusResponse {
  const stream = new ReadableStream<string>({
    start(controller) {
      controller.enqueue(errorText);
      controller.close();
    }
  });

  return {
    stream,
    getFinalText: async () => errorText,
    getWordCount: async () => 0, // Don't charge for errors
    getMetadata: async () => ({
      requestId,
      cached: false,
      latency: 0,
      providerResponses,
      moderatorLatency: 0,
      wordCount: 0,
    }),
  };
}
