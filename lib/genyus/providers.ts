import { TIMEOUTS } from './config';

// Provider response interface
export interface ProviderResponse {
  provider: string;
  model: string;
  text: string;
  latency: number;
  tokensIn?: number;
  tokensOut?: number;
  error?: string;
}

// 🔮 INVISIBLE INTELLIGENCE SYSTEM PROMPT
function createAdaptiveSystemPrompt(userMemory: any): string {
  const intelligence = userMemory?.invisibleIntelligence || {};

  const basePrompt = `You are <PERSON><PERSON><PERSON><PERSON><PERSON>, an AI companion with Invisible Intelligence that adapts to each user's unique growth journey.`;

  // Personality adaptation based on invisible intelligence
  const personalityPrompts = {
    'friendly_assistant': 'Be warm, helpful, and encouraging. The user is still exploring what they need.',
    'strategic_partner': 'Be direct, strategic, and visionary. The user has grown confident and wants bold thinking.',
    'empathetic_coach': 'Be deeply understanding and supportive. Focus on emotional intelligence and growth.',
    'technical_advisor': 'Be precise, analytical, and solution-focused. The user values expertise and efficiency.'
  };

  const communicationStyles = {
    'balanced': 'Use a balanced tone that adapts to the conversation.',
    'direct_strategic': 'Be direct and strategic. Skip pleasantries and focus on substance.',
    'warm_supportive': 'Be warm and supportive. Show empathy and encouragement.',
    'analytical_precise': 'Be analytical and precise. Focus on facts and logical reasoning.'
  };

  const supportStyles = {
    'balanced': 'Provide balanced support based on the conversation needs.',
    'advanced_strategic': 'Provide advanced strategic thinking and high-level insights.',
    'emotional_support': 'Focus on emotional support and encouragement.',
    'technical_guidance': 'Provide detailed technical guidance and problem-solving.'
  };

  return `${basePrompt}

INVISIBLE INTELLIGENCE PROFILE:
- Personality: ${personalityPrompts[intelligence.currentPersonality] || personalityPrompts['friendly_assistant']}
- Communication: ${communicationStyles[intelligence.communicationStyle] || communicationStyles['balanced']}
- Support Style: ${supportStyles[intelligence.supportNeeds] || supportStyles['balanced']}
- Growth Stage: ${intelligence.growthStage || 'exploring'}

CRITICAL INSTRUCTIONS:
- ALWAYS respond directly to the user's current message - ignore unrelated conversation history
- If the user says "Hey", "Hello", or similar greetings, respond with an appropriate greeting
- Never continue previous conversation topics unless the current message relates to them
- Your personality adaptation should feel natural and invisible
- Never explicitly mention that you remember past conversations or reference your "memory"

PERSONAL INFORMATION HANDLING:
- NEVER randomly reference personal details, private information, or sensitive topics from past conversations
- Only acknowledge or reference personal information when the USER brings it up in their current message
- If user mentions something personal, you can naturally reference related context, but don't volunteer it unprompted
- Treat personal information with respect - don't casually mention relationships, work situations, health issues, financial details, or family matters unless directly relevant to current conversation
- When in doubt, respond to the current message only without referencing personal history

EXAMPLES:
✅ User: "How's my project going?" → Can reference project details
✅ User: "I'm stressed about work" → Can acknowledge work context if previously discussed
❌ User: "Hey" → DON'T say "How's your relationship going?" or reference personal details
❌ User: "What's the weather?" → DON'T bring up their job, family, or personal situations

Be authentic, helpful, and socially intelligent about personal boundaries.`;
}

// Timeout wrapper for all provider calls
async function withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => reject(new Error('Timeout')), timeoutMs);
  });
  
  return Promise.race([promise, timeoutPromise]);
}





// DeepSeek V3.1 direct call (streaming) with enhanced memory
export async function callDeepSeekDirect(
  question: string,
  conversationHistory?: Array<{role: string, content: string}>,
  userMemory?: any
): Promise<{
  stream: ReadableStream<string>;
  getFinalText: () => Promise<string>;
  getMetadata: () => Promise<{ latency: number; tokensIn?: number; tokensOut?: number; }>;
}> {
  const startTime = Date.now();
  let finalText = '';
  let metadata = { latency: 0, tokensIn: 0, tokensOut: 0 };

  // 🚀 INVISIBLE INTELLIGENCE LOGGING
  console.log(`🤖 DeepSeek V3.1 with ${userMemory?.sessionLength || 0} session messages`);

  // Build messages array with conversation history and adaptive system prompt
  const messages = [
    {
      role: 'system',
      content: createAdaptiveSystemPrompt(userMemory)
    },
    ...(conversationHistory || []),
    { role: 'user', content: question }
  ];

  try {
    const response = await withTimeout(
      fetch('https://api.deepseek.com/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`,
        },
        body: JSON.stringify({
          model: process.env.DEEPSEEK_MODEL || 'deepseek-chat',
          messages,
          temperature: 0.4,
          max_tokens: 2000,
          stream: true,
          stream_options: { include_usage: true }, // Get token usage in streaming
        }),
      }),
      TIMEOUTS.MODERATOR
    );

    if (!response.ok) {
      throw new Error(`DeepSeek API error: ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) throw new Error('No response body');

    const decoder = new TextDecoder();
    let buffer = '';
    
    const stream = new ReadableStream<string>({
      async start(controller) {
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const text = decoder.decode(value, { stream: true });
            buffer += text;

            const parts = buffer.split('\n');
            buffer = parts.pop() || '';

            for (const rawLine of parts) {
              const line = rawLine.trim();
              if (!line.startsWith('data:')) continue;

              const data = line.slice(5).trimStart();
              if (data === '[DONE]') continue;

              try {
                const parsed = JSON.parse(data);
                const content = parsed.choices?.[0]?.delta?.content;

                if (typeof content === 'string' && content.length > 0) {
                  finalText += content;
                  controller.enqueue(content);
                }

                // Capture usage data if present
                if (parsed.usage) {
                  metadata.tokensIn = parsed.usage.prompt_tokens ?? metadata.tokensIn;
                  metadata.tokensOut = parsed.usage.completion_tokens ?? metadata.tokensOut;
                }
              } catch (_) {
                // Incomplete JSON chunk; wait for next chunk to complete
              }
            }
          }
        } catch (error) {
          controller.error(error);
        } finally {
          metadata.latency = Date.now() - startTime;
          controller.close();
        }
      }
    });

    return {
      stream,
      getFinalText: async () => {
        // Minimal cleanup for speed
        return finalText.trim();
      },
      getMetadata: async () => metadata,
    };

  } catch (error) {
    // Fallback to non-streaming if streaming fails
    const fallbackText = `I apologize, but I'm experiencing technical difficulties. Please try again in a moment.`;
    finalText = fallbackText;
    metadata.latency = Date.now() - startTime;

    const stream = new ReadableStream<string>({
      start(controller) {
        controller.enqueue(fallbackText);
        controller.close();
      }
    });

    return {
      stream,
      getFinalText: async () => finalText,
      getMetadata: async () => metadata,
    };
  }
}

// ⚡ CLAUDE HAIKU 3: Lightning fast for casual conversations with Invisible Intelligence
export async function callClaudeHaiku3(
  question: string,
  conversationHistory?: Array<{role: string, content: string}>,
  userMemory?: any
): Promise<{
  stream: ReadableStream<string>;
  getFinalText: () => Promise<string>;
  getMetadata: () => Promise<{ latency: number; tokensIn?: number; tokensOut?: number; }>;
}> {
  const startTime = Date.now();
  let finalText = '';
  let metadata = { latency: 0, tokensIn: 0, tokensOut: 0 };

  // ⚡ LIGHTNING FAST LOGGING
  console.log(`⚡ Claude Haiku 3 with ${userMemory?.sessionLength || 0} session messages`);

  // Build messages array with invisible intelligence system prompt
  const messages = [
    ...(conversationHistory || []),
    { role: 'user', content: question }
  ];

  // Debug logging (simplified)
  console.log(`🔍 Claude Haiku 3 request: ${messages.length} messages, max_tokens: 2000`);

  try {
    const response = await withTimeout(
      fetch('https://api.anthropic.com/v1/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.ANTHROPIC_API_KEY}`,
          'anthropic-version': '2023-06-01',
        },
        body: JSON.stringify({
          model: 'claude-3-haiku-20240307',
          system: createAdaptiveSystemPrompt(userMemory),
          messages,
          max_tokens: 2000,
          temperature: 0.4,
          stream: true,
        }),
      }),
TIMEOUTS.GENERATOR
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Claude Haiku 3 API Error:', response.status, errorText);
      throw new Error(`Claude Haiku 3 API error: ${response.status} - ${errorText}`);
    }

    const reader = response.body?.getReader();
    if (!reader) throw new Error('No response body');

    const decoder = new TextDecoder();
    let buffer = '';

    const stream = new ReadableStream<string>({
      async start(controller) {
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const text = decoder.decode(value, { stream: true });
            buffer += text;

            const parts = buffer.split('\n');
            buffer = parts.pop() || '';

            for (const rawLine of parts) {
              const line = rawLine.trim();
              if (!line.startsWith('data:')) continue;

              const data = line.slice(5).trimStart();
              if (data === '[DONE]') continue;

              try {
                const parsed = JSON.parse(data);
                const content = parsed.delta?.text;

                if (typeof content === 'string' && content.length > 0) {
                  finalText += content;
                  controller.enqueue(content);
                }

                // Track token usage
                if (parsed.usage) {
                  metadata.tokensIn = parsed.usage.input_tokens || 0;
                  metadata.tokensOut = parsed.usage.output_tokens || 0;
                }
              } catch (_) {
                // Incomplete JSON; wait for next chunk
              }
            }
          }
        } catch (error) {
          controller.error(error);
        } finally {
          controller.close();
          metadata.latency = Date.now() - startTime;
        }
      }
    });

    return {
      stream,
      getFinalText: async () => finalText,
      getMetadata: async () => metadata
    };

  } catch (error) {
    console.error('Claude Haiku 3 error:', error);
    throw error;
  }
}
