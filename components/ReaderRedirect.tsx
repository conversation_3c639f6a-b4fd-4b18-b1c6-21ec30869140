'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { createSupabaseClient } from '@/lib/supabase/client'

export function UserRedirect() {
  const router = useRouter()
  const supabase = createSupabaseClient()
  const [hasChecked, setHasChecked] = useState(false)

  useEffect(() => {
    // Prevent multiple redirects by checking if we've already processed this
    if (hasChecked) return

    const checkUserAndRedirect = async () => {
      try {
        // Add a small delay to ensure auth state is stable
        await new Promise(resolve => setTimeout(resolve, 100))

        const { data: { user } } = await supabase.auth.getUser()

        if (user) {
          console.log('UserRedirect: Authenticated user found, redirecting...')

          // Get user profile to check role
          const { data: profile } = await supabase
            .from('users')
            .select('role')
            .eq('id', user.id)
            .single()

          // Redirect based on role - all users go to timeline
          if (profile?.role === 'admin') {
            router.replace('/dashboard')
          } else if (profile?.role === 'user') {
            router.replace('/timeline')
          }
        } else {
          console.log('UserRedirect: No authenticated user, staying on homepage')
        }
      } catch (error) {
        // Ignore errors, just stay on homepage
        console.log('UserRedirect: User check failed:', error)
      } finally {
        setHasChecked(true)
      }
    }

    checkUserAndRedirect()
  }, [router, supabase, hasChecked])

  return null // This component doesn't render anything
}
