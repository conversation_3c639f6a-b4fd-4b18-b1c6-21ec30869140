-- SEE WHAT YOU HAVE
SELECT 
    u.*,
    au.last_sign_in_at,
    au.raw_app_meta_data->>'provider' as login_method
FROM users u
LEFT JOIN auth.users au ON u.id = au.id
ORDER BY u.created_at DESC;

-- COUNT BY ROLE
SELECT role, COUNT(*) 
FROM users 
GROUP BY role;

-- FIX IT - Update all logged-in visitors to 'user' role
UPDATE users
SET role = 'user'
WHERE role = 'visitor'
AND id IN (
    SELECT u.id 
    FROM users u
    JOIN auth.users au ON u.id = au.id
    WHERE au.last_sign_in_at IS NOT NULL
);