-- First, let's see what columns exist in the users table
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'users'
ORDER BY ordinal_position;

-- Check all users and their application roles
SELECT 
    u.id,
    u.email,
    u.role as app_role,
    u.is_creator,
    u.created_at,
    u.last_seen,
    au.raw_app_meta_data->>'provider' as login_method,
    au.last_sign_in_at,
    CASE 
        WHEN u.role = 'visitor' AND au.last_sign_in_at IS NOT NULL THEN 'ISSUE: Logged-in visitor'
        WHEN u.role = 'visitor' AND au.last_sign_in_at IS NULL THEN 'OK: Never logged in'
        ELSE 'OK'
    END as status_check
FROM users u
LEFT JOIN auth.users au ON u.id = au.id
ORDER BY u.created_at DESC;

-- Count users by application role
SELECT 
    role as app_role,
    COUNT(*) as user_count,
    COUNT(CASE WHEN is_creator = true THEN 1 END) as creators_count
FROM users
GROUP BY role
ORDER BY user_count DESC;

-- Find problematic visitors who have actually logged in
SELECT 
    u.id,
    u.email,
    u.role,
    u.is_creator,
    au.raw_app_meta_data->>'provider' as login_provider,
    au.last_sign_in_at,
    u.created_at
FROM users u
JOIN auth.users au ON u.id = au.id
WHERE u.role = 'visitor' 
AND au.last_sign_in_at IS NOT NULL
ORDER BY au.last_sign_in_at DESC;

-- Check the user_role enum values
SELECT 
    t.typname AS enum_name,
    e.enumlabel AS enum_value
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid
WHERE t.typname = 'user_role'
ORDER BY e.enumsortorder;

-- Update statement to fix logged-in visitors (DO NOT RUN YET - review results first)
/*
UPDATE users
SET role = 'user'
WHERE id IN (
    SELECT u.id
    FROM users u
    JOIN auth.users au ON u.id = au.id
    WHERE u.role = 'visitor' 
    AND au.last_sign_in_at IS NOT NULL
);
*/