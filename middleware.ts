import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const url = new URL(request.url)

  // Debug logging for production redirect loop
  console.log('Middleware:', {
    hostname: url.hostname,
    pathname: url.pathname,
    protocol: url.protocol,
    headers: {
      'x-forwarded-proto': request.headers.get('x-forwarded-proto'),
      'x-forwarded-host': request.headers.get('x-forwarded-host'),
    }
  })

  // TEMPORARILY DISABLED: Canonical domain redirect to fix redirect loop
  // 1) Canonical domain: redirect www → apex to keep auth cookies consistent
  // if (url.hostname.startsWith('www.')) {
  //   url.hostname = url.hostname.slice(4)
  //   return NextResponse.redirect(url, 308)
  // }

  // Clone the response
  const response = NextResponse.next()

  // Security headers
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  response.headers.set('X-XSS-Protection', '1; mode=block')

  // Permissions Policy - allow microphone for audio features, restrict others
  response.headers.set('Permissions-Policy', 'microphone=self, camera=(), geolocation=(), payment=(), usb=(), bluetooth=()')

  // Content Security Policy (basic)
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-eval' 'unsafe-inline' https://js.stripe.com https://checkout.stripe.com https://va.vercel-scripts.com https://www.googletagmanager.com https://connect.facebook.net",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: blob:",
    "media-src 'self' https: blob:",
    "connect-src 'self' https: wss:",
    "frame-src https://js.stripe.com https://checkout.stripe.com",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ].join('; ')

  response.headers.set('Content-Security-Policy', csp)

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next (all Next.js internal files including static assets)
     * - favicon.ico (favicon file)
     * - static files (images, fonts, etc.)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp|ico|woff|woff2|ttf|eot|css|js|mjs)).*)',
  ],
}
